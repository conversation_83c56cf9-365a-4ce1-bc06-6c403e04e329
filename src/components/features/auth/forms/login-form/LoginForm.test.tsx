import { LoginForm } from '@/components/features/auth';
import { authService } from '@/services/auth/auth.service';
import useMediaQuery from '@mui/material/useMediaQuery';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import i18n from '@/providers/i18n/i18n';
import { transformText } from '@/utils/text-transform.util';

// Page component imported above

const refreshMock = vi.fn();
const checkSessionMock = vi.fn();
const pushMock = vi.fn();

// Mock window.scrollTo for JSDOM
Object.defineProperty(window, 'scrollTo', {
  value: vi.fn(),
  writable: true,
});

// Mock the custom useRouter hook
vi.mock('@/hooks/navigation/use-router.hook', () => ({
  useRouter: () => ({
    refresh: refreshMock,
    push: pushMock,
  }),
}));

// Mock useAuth hook
vi.mock('@/hooks/auth/use-auth.hook', () => ({
  useAuth: () => ({
    checkSession: checkSessionMock,
  }),
}));

// Mock useMediaQuery
vi.mock('@mui/material/useMediaQuery', () => ({
  __esModule: true,
  default: vi.fn(),
}));

describe('Login Form', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  it('should show success alert when valid credentials are submitted', async () => {
    // Mock successful login
    vi.spyOn(authService, 'signInWithPassword').mockResolvedValueOnce({
      message: 'Login successful',
      data: {
        accessId: '************',
      },
    });

    render(<LoginForm />);

    const usernameInput = await screen.findByTestId('username');
    const passwordInput = await screen.findByTestId('password');
    const signInButton = await screen.findByTestId('submit');

    await userEvent.clear(usernameInput);
    await userEvent.type(usernameInput, '<EMAIL>');

    await userEvent.clear(passwordInput);
    await userEvent.type(passwordInput, 'DemoPass123');

    await userEvent.click(signInButton);

    await waitFor(() => {
      expect(
        screen.getByRole('heading', {
          name: transformText(i18n.t('auth.sentence.loginSuccessful'), 'sentenceCase'),
        })
      ).toBeInTheDocument();
      expect(checkSessionMock).toHaveBeenCalled();
      expect(refreshMock).toHaveBeenCalled();
    });
  });

  it('should show error alert when invalid credentials are submitted', async () => {
    // Mock failed login
    vi.spyOn(authService, 'signInWithPassword').mockResolvedValueOnce({
      message:
        'the username or password is incorrect. please enter a valid username and password to try again',
      errorCode: '20001',
      meta: {},
    } as any);

    render(<LoginForm />);

    const usernameInput = await screen.findByTestId('username');
    const passwordInput = await screen.findByTestId('password');
    const signInButton = await screen.findByTestId('submit');

    await userEvent.clear(usernameInput);
    await userEvent.type(usernameInput, '<EMAIL>');

    await userEvent.clear(passwordInput);
    await userEvent.type(passwordInput, 'WrongPass');

    await userEvent.click(signInButton);

    await waitFor(() => {
      expect(
        screen.getByRole('heading', {
          name: transformText(i18n.t('error.auth.sentence.invalidCredentials'), 'sentenceCase'),
        })
      ).toBeInTheDocument();
      expect(checkSessionMock).not.toHaveBeenCalled();
      expect(refreshMock).not.toHaveBeenCalled();
    });
  });

  it('should expose a reset password link that points to the reset password page', async () => {
    render(<LoginForm />);

    const forgotLink = await screen.findByTestId('forgotPassword');

    // NextLink renders absolute URLs in JSDOM, so we check it contains the expected path
    expect(forgotLink).toHaveAttribute('href');
    expect(forgotLink.getAttribute('href') || '').toContain('/reset-password');
  });

  it('should render horizontal divider on mobile', () => {
    // Mock useMediaQuery to return true for mobile
    vi.mocked(useMediaQuery).mockReturnValue(true);

    render(<LoginForm />);

    const divider = screen.getByRole('separator');
    expect(divider).toHaveAttribute('aria-orientation', 'horizontal');
  });

  it('should render vertical divider on desktop', () => {
    // Mock useMediaQuery to return false for desktop
    vi.mocked(useMediaQuery).mockReturnValue(false);
    render(<LoginForm />);

    const divider = screen.getByRole('separator');
    expect(divider).toHaveAttribute('aria-orientation', 'vertical');
  });
});

describe('OAuth Login Form', () => {
  it('should trigger Google One Tap login and handle callback', async () => {
    // Spy on auth service
    const googleLoginSpy = vi.spyOn(authService, 'signInWithGoogle').mockResolvedValueOnce({
      message: 'Login successful',
      data: {
        accessId: '************',
      },
    });

    let capturedCallback: (response: { credential: string }) => void = () => {};

    // Properly mock window.google.accounts.id
    globalThis.window.google = {
      accounts: {
        id: {
          initialize: vi.fn((config) => {
            capturedCallback = config.callback;
          }),
          prompt: vi.fn(),
        },
      },
    };

    render(<LoginForm />);

    const accessIdInput = screen.getByTestId('oauthAccessId');
    await userEvent.type(accessIdInput, '************');

    const googleBtn = screen.getByTestId('oauthSignIn');
    await userEvent.click(googleBtn);

    // Simulate Google callback
    capturedCallback({ credential: 'fake-google-id-token' });

    // Auth service should be called
    expect(googleLoginSpy).toHaveBeenCalledWith({
      accessId: '************',
      idToken: 'fake-google-id-token',
    });

    await waitFor(() => {
      expect(
        screen.getByRole('heading', {
          name: transformText(i18n.t('auth.sentence.loginSuccessful'), 'sentenceCase'),
        })
      ).toBeInTheDocument();
      expect(checkSessionMock).toHaveBeenCalled();
      expect(refreshMock).toHaveBeenCalled();
    });
  });

  it('should log error if Google SDK is not available', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    // Remove window.google
    delete window.google;

    render(<LoginForm />);

    const accessIdInput = screen.getByTestId('oauthAccessId');
    await userEvent.type(accessIdInput, '************');

    const googleBtn = screen.getByTestId('oauthSignIn');
    await userEvent.click(googleBtn);

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Failed to initialize google OAuth:',
      expect.objectContaining({
        message: 'Google SDK not loaded',
      })
    );

    consoleErrorSpy.mockRestore();
  });

  it('Redirect to pending approval page when pendingApproval cache is true', () => {
    localStorage.setItem('pendingApproval', 'true');

    render(<LoginForm />);

    expect(pushMock).toHaveBeenCalledWith('/pending-approval');
  });
});
